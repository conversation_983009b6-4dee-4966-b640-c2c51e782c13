//
//  Shaders.metal
//  Test2Dlib
//
//  Created by 王雷 on 4/6/25.
//

#include <metal_stdlib>
using namespace metal;

// MARK: - 统一的数据结构

// 统一3D顶点结构
struct UnifiedVertex {
    float3 position [[attribute(0)]];
    float3 normal [[attribute(1)]];
};

// 2D文字顶点结构
struct TextVertex {
    float2 position [[attribute(0)]];
};

struct Transform {
    float4x4 modelMatrix;
    float4x4 viewMatrix;
    float4x4 projectionMatrix;
};

// MARK: - 输出结构

struct UnifiedVertexOut {
    float4 position [[position]];
    float3 worldPosition;
    float3 normal;
};

struct TextVertexOut {
    float4 position [[position]];
};

// MARK: - 统一3D几何着色器

vertex UnifiedVertexOut unified_vertex(UnifiedVertex in [[stage_in]],
                                      constant Transform& transform [[buffer(1)]],
                                      constant float4x4& modelMatrix [[buffer(2)]]) {
    UnifiedVertexOut out;
    
    // 计算完整的变换矩阵
    float4x4 mvpMatrix = transform.projectionMatrix * transform.viewMatrix * modelMatrix;
    
    // 变换顶点位置
    float4 worldPosition = modelMatrix * float4(in.position, 1.0);
    out.position = mvpMatrix * float4(in.position, 1.0);
    out.worldPosition = worldPosition.xyz;
    
    // 变换法向量
    float3x3 normalMatrix = float3x3(modelMatrix.columns[0].xyz,
                                   modelMatrix.columns[1].xyz,
                                   modelMatrix.columns[2].xyz);
    out.normal = normalize(normalMatrix * in.normal);
    
    return out;
}

fragment float4 unified_fragment(UnifiedVertexOut in [[stage_in]],
                                constant float4& color [[buffer(0)]]) {
    return color;
}

// MARK: - 独立2D文字着色器

vertex TextVertexOut text_vertex(TextVertex in [[stage_in]],
                                constant Transform& transform [[buffer(1)]],
                                constant float4x4& modelMatrix [[buffer(2)]]) {
    TextVertexOut out;
    
    // 2D文字直接使用投影变换
    float4x4 mvpMatrix = transform.projectionMatrix * transform.viewMatrix * modelMatrix;
    out.position = mvpMatrix * float4(in.position, 0.0, 1.0);
    
    return out;
}

fragment float4 text_fragment(TextVertexOut in [[stage_in]],
                             constant float4& color [[buffer(0)]]) {
    return color;
}

// MARK: - 抗锯齿线条着色器

// 抗锯齿线条顶点结构
struct AntiAliasedLineVertex {
    float3 position [[attribute(0)]];
    float2 direction [[attribute(1)]];
    float offset [[attribute(2)]];
};

// 线条渲染统一参数
struct LineUniforms {
    float lineWidth;
    float screenWidth;
    float screenHeight;
    float antialiasingWidth;
};

// 抗锯齿线条顶点输出
struct AntiAliasedLineVertexOut {
    float4 position [[position]];
    float distance; // 到线条中心的距离
    float lineWidth;
};

// 抗锯齿线条顶点着色器
vertex AntiAliasedLineVertexOut orbitLineVertex(AntiAliasedLineVertex in [[stage_in]],
                                               constant Transform& transform [[buffer(1)]],
                                               constant float4x4& modelMatrix [[buffer(2)]],
                                               constant LineUniforms& lineUniforms [[buffer(3)]]) {
    AntiAliasedLineVertexOut out;
    
    // 计算完整的变换矩阵
    float4x4 mvpMatrix = transform.projectionMatrix * transform.viewMatrix * modelMatrix;
    
    // 基础位置变换
    float4 basePosition = mvpMatrix * float4(in.position, 1.0);
    
    // 计算屏幕空间的线条方向
    float2 screenDir = normalize(in.direction);
    
    // 计算垂直于线条的偏移方向
    float2 perpDir = float2(-screenDir.y, screenDir.x);
    
    // 应用厚度偏移（像素空间）
    float pixelOffsetX = (perpDir.x * in.offset * lineUniforms.lineWidth) / lineUniforms.screenWidth;
    float pixelOffsetY = (perpDir.y * in.offset * lineUniforms.lineWidth) / lineUniforms.screenHeight;
    
    // 应用偏移到最终位置
    out.position = basePosition;
    out.position.x += pixelOffsetX * basePosition.w;
    out.position.y += pixelOffsetY * basePosition.w;
    
    // 传递距离信息用于抗锯齿计算
    out.distance = in.offset * lineUniforms.lineWidth * 0.5;
    out.lineWidth = lineUniforms.lineWidth;
    
    return out;
}

// 抗锯齿线条片段着色器
fragment float4 orbitLineFragment(AntiAliasedLineVertexOut in [[stage_in]],
                                 constant float4& color [[buffer(0)]],
                                 constant LineUniforms& lineUniforms [[buffer(1)]]) {
    // 计算到线条中心的距离
    float dist = abs(in.distance);
    
    // 线条半宽
    float halfWidth = in.lineWidth * 0.5;
    
    // 抗锯齿边缘宽度
    float edgeWidth = lineUniforms.antialiasingWidth;
    
    // 计算透明度，使用smooth step实现抗锯齿
    float alpha = 1.0 - smoothstep(halfWidth - edgeWidth, halfWidth + edgeWidth, dist);
    
    // 额外的边缘柔化
    alpha = pow(alpha, 0.8); // 轻微的gamma校正
    
    return float4(color.rgb, color.a * alpha);
}