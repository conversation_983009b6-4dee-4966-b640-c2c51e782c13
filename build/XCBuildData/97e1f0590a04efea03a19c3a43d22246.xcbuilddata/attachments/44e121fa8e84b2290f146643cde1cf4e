-target x86_64-apple-macos10.13 -fmodules -gmodules '-fmodule-name=libtess2' -fpascal-strings -Os -DSWIFT_PACKAGE -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -fasm-blocks -g -I/Users/<USER>/Library/Developer/Xcode/DerivedData/Test2Dlib-duvuywzpyevifncvechurpzclsdq/SourcePackages/checkouts/LibTessSwift/build/Release/include -I/Users/<USER>/Library/Developer/Xcode/DerivedData/Test2Dlib-duvuywzpyevifncvechurpzclsdq/SourcePackages/checkouts/LibTessSwift/Sources/libtess2/include -I/Users/<USER>/Library/Developer/Xcode/DerivedData/Test2Dlib-duvuywzpyevifncvechurpzclsdq/SourcePackages/checkouts/LibTessSwift/build/LibTessSwift.build/Release/libtess2.build/DerivedSources-normal/x86_64 -I/Users/<USER>/Library/Developer/Xcode/DerivedData/Test2Dlib-duvuywzpyevifncvechurpzclsdq/SourcePackages/checkouts/LibTessSwift/build/LibTessSwift.build/Release/libtess2.build/DerivedSources/x86_64 -I/Users/<USER>/Library/Developer/Xcode/DerivedData/Test2Dlib-duvuywzpyevifncvechurpzclsdq/SourcePackages/checkouts/LibTessSwift/build/LibTessSwift.build/Release/libtess2.build/DerivedSources -F/Users/<USER>/Library/Developer/Xcode/DerivedData/Test2Dlib-duvuywzpyevifncvechurpzclsdq/SourcePackages/checkouts/LibTessSwift/build/Release -iframework /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks -DXcode