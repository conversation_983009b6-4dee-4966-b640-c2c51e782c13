{"buildCommand": {"command": "build", "skipDependencies": false, "style": "buildAndRun"}, "configuredTargets": [{"guid": "51d47c4a23422371a620e3177ad80d431d3f63c4979a8e5ffb820f144bed5923"}], "containerPath": "/Users/<USER>/Downloads/Test2Dlib/Test2Dlib.xcodeproj", "continueBuildingAfterErrors": false, "dependencyScope": "workspace", "enableIndexBuildArena": false, "hideShellScriptEnvironment": false, "parameters": {"action": "build", "overrides": {"commandLine": {"table": {}}, "synthesized": {"table": {"ACTION": "build", "COLOR_DIAGNOSTICS": "YES", "diagnostic_message_length": "96", "ENABLE_PREVIEWS": "NO", "ENABLE_XOJIT_PREVIEWS": "NO"}}}}, "showNonLoggedProgress": true, "useDryRun": false, "useImplicitDependencies": false, "useLegacyBuildLocations": false, "useParallelTargets": true}